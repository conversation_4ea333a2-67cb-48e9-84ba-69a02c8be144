"""
Validation Utility Functions

This module provides reusable validation utilities
to reduce code duplication and improve maintainability.
"""

import re
from typing import Any, Dict, List, Optional, Union

from fastapi import HTTPException, status

from .db_utils import exists_in_table, get_single_value
from .logger import debug, error, warning


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that required fields are present in data.

    Args:
        data: Dictionary to validate
        required_fields: List of required field names

    Raises:
        HTTPException: If any required field is missing
    """
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]

    if missing_fields:
        error(f"Missing required fields: {missing_fields}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing required fields: {', '.join(missing_fields)}"
        )


def validate_string_length(value: str, field_name: str, min_length: int = 1, max_length: Optional[int] = None) -> None:
    """
    Validate string length.

    Args:
        value: String value to validate
        field_name: Name of the field for error messages
        min_length: Minimum length required
        max_length: Maximum length allowed

    Raises:
        HTTPException: If validation fails
    """
    if not isinstance(value, str):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{field_name} must be a string")

    if len(value) < min_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be at least {min_length} characters long",
        )

    if max_length and len(value) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"{field_name} must be at most {max_length} characters long"
        )


def validate_email_format(email: str) -> bool:
    """
    Validate email format using regex.

    Args:
        email: Email address to validate

    Returns:
        True if email format is valid
    """
    if not email:
        return False

    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(email_pattern, email) is not None


def validate_skill_ids(skill_ids: List[int]) -> None:
    """
    Validate that skill IDs exist in the database.

    Args:
        skill_ids: List of skill IDs to validate

    Raises:
        HTTPException: If any skill ID is invalid
    """
    if not skill_ids:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="At least one skill ID is required")

    # Check if all skill IDs exist
    for skill_id in skill_ids:
        if not isinstance(skill_id, int) or skill_id <= 0:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid skill ID format: {skill_id}")

        if not exists_in_table("skills", {"id": skill_id}):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Skill ID {skill_id} not found")


def validate_assessment_id(assessment_id: Union[int, str]) -> int:
    """
    Validate and convert assessment ID.

    Args:
        assessment_id: Assessment ID to validate

    Returns:
        Valid assessment ID as integer

    Raises:
        HTTPException: If assessment ID is invalid
    """
    try:
        assessment_id = int(assessment_id)
    except (ValueError, TypeError):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid assessment ID format")

    if not exists_in_table("assessments", {"id": assessment_id}):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Assessment ID {assessment_id} not found")

    return assessment_id


def validate_user_id(user_id: str) -> None:
    """
    Validate user ID format and existence.

    Args:
        user_id: User ID to validate

    Raises:
        HTTPException: If user ID is invalid
    """
    if not user_id or not isinstance(user_id, str):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID is required and must be a string")

    # Check if user exists
    if not exists_in_table("users", {"external_id": user_id}):
        debug(f"User {user_id} not found in database")
        # For now, we'll just log this - some endpoints may create users on-demand


def validate_question_selection_mode(mode: str) -> None:
    """
    Validate question selection mode.

    Args:
        mode: Question selection mode to validate

    Raises:
        HTTPException: If mode is invalid
    """
    valid_modes = ["fixed", "dynamic"]

    if mode not in valid_modes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid question selection mode. Must be one of: {', '.join(valid_modes)}",
        )


def validate_difficulty_level(level: str) -> None:
    """
    Validate difficulty level.

    Args:
        level: Difficulty level to validate

    Raises:
        HTTPException: If level is invalid
    """
    valid_levels = ["easy", "intermediate", "advanced"]

    if level.lower() not in valid_levels:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid difficulty level. Must be one of: {', '.join(valid_levels)}",
        )


def validate_duration(duration: int, min_duration: int = 1, max_duration: int = 180) -> None:
    """
    Validate assessment duration.

    Args:
        duration: Duration in minutes
        min_duration: Minimum duration allowed
        max_duration: Maximum duration allowed

    Raises:
        HTTPException: If duration is invalid
    """
    if not isinstance(duration, int) or duration < min_duration or duration > max_duration:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Duration must be between {min_duration} and {max_duration} minutes",
        )


def validate_json_structure(data: Any, required_keys: List[str]) -> None:
    """
    Validate JSON structure has required keys.

    Args:
        data: Data to validate
        required_keys: List of required keys

    Raises:
        HTTPException: If structure is invalid
    """
    if not isinstance(data, dict):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Data must be a valid JSON object")

    missing_keys = [key for key in required_keys if key not in data]

    if missing_keys:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing required keys: {', '.join(missing_keys)}"
        )


def validate_pagination_params(limit: int, offset: int) -> None:
    """
    Validate pagination parameters.

    Args:
        limit: Number of items per page
        offset: Starting position

    Raises:
        HTTPException: If parameters are invalid
    """
    if limit <= 0 or limit > 100:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Limit must be between 1 and 100")

    if offset < 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Offset must be non-negative")


def sanitize_input(input_string: str, max_length: int = 1000) -> str:
    """
    Sanitize input string by removing/escaping potentially dangerous characters.

    Args:
        input_string: String to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized string
    """
    if not isinstance(input_string, str):
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';()&+]', "", input_string)

    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    return sanitized.strip()


def validate_skill_name(skill_name: str) -> None:
    """
    Validate skill name format and uniqueness.

    Args:
        skill_name: Skill name to validate

    Raises:
        HTTPException: If skill name is invalid
    """
    validate_string_length(skill_name, "Skill name", min_length=2, max_length=100)

    # Check for invalid characters
    if not re.match(r"^[a-zA-Z0-9\s\-_\.]+$", skill_name):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Skill name contains invalid characters")

    # Check uniqueness
    if exists_in_table("skills", {"name": skill_name}):
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"Skill '{skill_name}' already exists")


def validate_assessment_name(assessment_name: str) -> None:
    """
    Validate assessment name format.

    Args:
        assessment_name: Assessment name to validate

    Raises:
        HTTPException: If assessment name is invalid
    """
    validate_string_length(assessment_name, "Assessment name", min_length=3, max_length=200)

    # Check for invalid characters
    if not re.match(r"^[a-zA-Z0-9\s\-_\.]+$", assessment_name):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Assessment name contains invalid characters"
        )
