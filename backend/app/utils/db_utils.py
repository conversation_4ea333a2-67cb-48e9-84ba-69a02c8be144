"""
Database Utility Functions

This module provides reusable database connection and query utilities
to reduce code duplication and improve maintainability.
"""

import json
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Union

import psycopg2
import psycopg2.extras
from fastapi import HTTPException, status

from ..config.db_config import DATABASE_CONFIG
from .logger import debug, error, info, warning


@contextmanager
def get_db_connection(cursor_factory=None):
    """
    Context manager for database connections with automatic cleanup.

    Args:
        cursor_factory: Optional cursor factory (e.g., psycopg2.extras.DictCursor)

    Yields:
        Database connection object
    """
    conn = None
    try:
        conn = psycopg2.connect(**DATABASE_CONFIG)
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        error(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            conn.close()


@contextmanager
def get_db_cursor(cursor_factory=None):
    """
    Context manager for database cursors with automatic cleanup.

    Args:
        cursor_factory: Optional cursor factory (e.g., psycopg2.extras.DictCursor)

    Yields:
        Tuple of (connection, cursor)
    """
    conn = None
    cursor = None
    try:
        conn = psycopg2.connect(**DATABASE_CONFIG)
        cursor = conn.cursor(cursor_factory=cursor_factory)
        yield conn, cursor
    except Exception as e:
        if conn:
            conn.rollback()
        error(f"Database cursor error: {e}")
        raise
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def execute_query(
    query: str,
    params: Optional[Tuple] = None,
    fetch_one: bool = False,
    fetch_all: bool = False,
    cursor_factory=None,
    commit: bool = False,
) -> Optional[Union[Tuple, List[Tuple], Dict, List[Dict]]]:
    """
    Execute a database query with error handling and logging.

    Args:
        query: SQL query string
        params: Query parameters
        fetch_one: Whether to fetch one result
        fetch_all: Whether to fetch all results
        cursor_factory: Optional cursor factory
        commit: Whether to commit the transaction

    Returns:
        Query result or None
    """
    debug(f"Executing query: {query[:100]}...")

    try:
        with get_db_cursor(cursor_factory=cursor_factory) as (conn, cursor):
            cursor.execute(query, params)

            if commit:
                conn.commit()

            if fetch_one:
                result = cursor.fetchone()
                debug(f"Query returned one result: {bool(result)}")
                return result
            elif fetch_all:
                result = cursor.fetchall()
                debug(f"Query returned {len(result)} results")
                return result
            else:
                return None

    except Exception as e:
        error(f"Query execution failed: {e}")
        raise


def execute_transaction(queries: List[Dict[str, Any]], cursor_factory=None) -> bool:
    """
    Execute multiple queries in a single transaction.

    Args:
        queries: List of dictionaries with 'query' and optional 'params' keys
        cursor_factory: Optional cursor factory

    Returns:
        True if all queries executed successfully
    """
    debug(f"Executing transaction with {len(queries)} queries")

    try:
        with get_db_cursor(cursor_factory=cursor_factory) as (conn, cursor):
            for query_info in queries:
                query = query_info["query"]
                params = query_info.get("params")
                cursor.execute(query, params)

            conn.commit()
            debug("Transaction committed successfully")
            return True

    except Exception as e:
        error(f"Transaction failed: {e}")
        raise


def get_single_value(query: str, params: Optional[Tuple] = None, default: Any = None) -> Any:
    """
    Execute a query and return a single value.

    Args:
        query: SQL query string
        params: Query parameters
        default: Default value if no result found

    Returns:
        Single value from the query result
    """
    result = execute_query(query, params, fetch_one=True)
    if result:
        return result[0]
    return default


def get_single_row(
    query: str,
    params: Optional[Tuple] = None,
    as_dict: bool = False,
) -> Optional[Union[Tuple, Dict]]:
    """
    Execute a query and return a single row.

    Args:
        query: SQL query string
        params: Query parameters
        as_dict: Whether to return result as dictionary

    Returns:
        Single row from the query result
    """
    cursor_factory = psycopg2.extras.DictCursor if as_dict else None
    result = execute_query(query, params, fetch_one=True, cursor_factory=cursor_factory)

    if result and as_dict:
        return dict(result)
    return result


def get_multiple_rows(
    query: str,
    params: Optional[Tuple] = None,
    as_dict: bool = False,
) -> List[Union[Tuple, Dict]]:
    """
    Execute a query and return multiple rows.

    Args:
        query: SQL query string
        params: Query parameters
        as_dict: Whether to return results as dictionaries

    Returns:
        List of rows from the query result
    """
    cursor_factory = psycopg2.extras.DictCursor if as_dict else None
    results = execute_query(query, params, fetch_all=True, cursor_factory=cursor_factory)

    if results and as_dict:
        return [dict(row) for row in results]
    return results or []


def insert_or_update(
    table: str,
    data: Dict[str, Any],
    conflict_columns: Optional[List[str]] = None,
    update_columns: Optional[List[str]] = None,
) -> bool:
    """
    Insert a record or update on conflict.

    Args:
        table: Table name
        data: Dictionary of column-value pairs
        conflict_columns: Columns to check for conflicts
        update_columns: Columns to update on conflict

    Returns:
        True if operation was successful
    """
    columns = list(data.keys())
    placeholders = ", ".join(["%s"] * len(columns))
    values = list(data.values())

    query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"

    if conflict_columns:
        query += f" ON CONFLICT ({', '.join(conflict_columns)})"

        if update_columns:
            updates = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])
            query += f" DO UPDATE SET {updates}"
        else:
            query += " DO NOTHING"

    debug(f"Insert/Update query: {query}")

    try:
        execute_query(query, tuple(values), commit=True)
        return True
    except Exception as e:
        error(f"Insert/Update failed: {e}")
        raise


def exists_in_table(
    table: str,
    conditions: Dict[str, Any],
) -> bool:
    """
    Check if a record exists in a table.

    Args:
        table: Table name
        conditions: Dictionary of column-value pairs for WHERE clause

    Returns:
        True if record exists
    """
    where_clause = " AND ".join([f"{col} = %s" for col in conditions.keys()])
    query = f"SELECT 1 FROM {table} WHERE {where_clause} LIMIT 1"

    result = execute_query(query, tuple(conditions.values()), fetch_one=True)
    return bool(result)


def get_table_count(table: str, conditions: Optional[Dict[str, Any]] = None) -> int:
    """
    Get the count of records in a table.

    Args:
        table: Table name
        conditions: Optional dictionary of column-value pairs for WHERE clause

    Returns:
        Number of records
    """
    query = f"SELECT COUNT(*) FROM {table}"
    params = None

    if conditions:
        where_clause = " AND ".join([f"{col} = %s" for col in conditions.keys()])
        query += f" WHERE {where_clause}"
        params = tuple(conditions.values())

    return get_single_value(query, params, default=0)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    Safely parse JSON string with error handling.

    Args:
        json_str: JSON string to parse
        default: Default value if parsing fails

    Returns:
        Parsed JSON object or default value
    """
    try:
        return json.loads(json_str) if json_str else default
    except (json.JSONDecodeError, TypeError) as e:
        warning(f"Failed to parse JSON: {e}")
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    Safely serialize object to JSON string with error handling.

    Args:
        obj: Object to serialize
        default: Default value if serialization fails

    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(obj)
    except (TypeError, ValueError) as e:
        warning(f"Failed to serialize JSON: {e}")
        return default
